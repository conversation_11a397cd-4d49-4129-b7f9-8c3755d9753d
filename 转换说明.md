# NC文件转ASC文件转换说明

## 转换结果

已成功将您的NC格式PET数据文件转换为ASC格式文件。

### 转换的文件
- **pet_2023.nc** → 12个ASC文件 (pet_2023_month_01.asc 到 pet_2023_month_12.asc)
- **pet_2024.nc** → 12个ASC文件 (pet_2024_month_01.asc 到 pet_2024_month_12.asc)

### 文件位置
- 2023年ASC文件位于：`pet_2023/` 文件夹
- 2024年ASC文件位于：`pet_2024/` 文件夹

## ASC文件格式说明

生成的ASC文件采用标准的ESRI ASCII Grid格式，包含以下信息：

### 文件头信息
```
ncols         7849                    # 列数
nrows         5146                    # 行数  
xllcorner     71.29005533854166      # 左下角X坐标
yllcorner     15.756290691830095     # 左下角Y坐标
cellsize      0.00833333333333286    # 像元大小
NODATA_value  -9999                  # 无数据值
```

### 数据特征
- **数据范围**：经度 71.29° - 136.71°，纬度 15.76° - 58.61°
- **空间分辨率**：约0.0083度（约1公里）
- **数据单位**：月潜在蒸散发量（mm）
- **坐标系统**：WGS84地理坐标系

## 在ArcMap中使用

1. **直接加载**：
   - 在ArcMap中，使用"Add Data"直接添加ASC文件
   - 文件会自动识别为栅格数据

2. **坐标系统设置**：
   - 数据已包含WGS84坐标系统信息
   - 如需投影，可使用"Project Raster"工具

3. **数据分析**：
   - 可直接进行栅格计算、统计分析等操作
   - 支持所有ArcMap栅格分析工具

## 文件命名规则

- `pet_YYYY_month_MM.asc`
  - YYYY：年份（2023或2024）
  - MM：月份（01-12）

例如：
- `pet_2023_month_01.asc` = 2023年1月数据
- `pet_2023_month_07.asc` = 2023年7月数据

## 数据质量检查

转换过程中已进行以下检查：
- ✅ 维度信息正确识别（时间、经度、纬度）
- ✅ 地理坐标正确提取
- ✅ 数据完整性验证
- ✅ 12个月数据全部转换成功

## 使用的转换脚本

转换使用了以下Python脚本：
- `nc_to_asc_converter.py` - 主转换脚本
- `check_dependencies.py` - 依赖库检查脚本

## 技术细节

### 原始NC文件信息
- **变量名**：etp (monthly potential evapotranspiration)
- **维度**：time(12) × lat(5146) × lon(7849)
- **数据类型**：浮点数
- **压缩格式**：NetCDF4

### 转换处理
- 自动识别时间维度和数据变量
- 提取地理坐标信息
- 数据翻转处理（NC文件通常从北到南，ASC文件从南到北）
- 无效数据标记为-9999

## 注意事项

1. **文件大小**：每个ASC文件约150-220MB，请确保有足够存储空间
2. **内存需求**：处理大文件时需要足够内存
3. **坐标系统**：数据使用WGS84坐标系，如需其他坐标系请在GIS软件中转换
4. **数据精度**：保持了原始NC文件的数据精度

## 后续使用建议

1. **备份原始数据**：建议保留原始NC文件作为备份
2. **批量处理**：如需处理更多年份数据，可重复运行转换脚本
3. **质量检查**：建议在GIS软件中检查数据范围和统计值
4. **文档记录**：建议记录数据来源和处理过程

---

转换完成时间：2024年
转换工具：Python + netCDF4 + numpy
