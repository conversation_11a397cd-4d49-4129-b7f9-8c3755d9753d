#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NC文件转ASC文件转换器
将包含12个月数据的NC文件转换为12个独立的ASC文件
"""

import os
import sys
import numpy as np
import netCDF4 as nc
from pathlib import Path

def check_nc_structure(nc_file_path):
    """检查NC文件的结构和维度信息"""
    print(f"正在检查文件: {nc_file_path}")
    
    try:
        with nc.Dataset(nc_file_path, 'r') as dataset:
            print("\n=== NC文件信息 ===")
            print(f"维度: {list(dataset.dimensions.keys())}")
            print(f"变量: {list(dataset.variables.keys())}")
            
            # 打印维度详细信息
            for dim_name, dim in dataset.dimensions.items():
                print(f"维度 '{dim_name}': 大小 = {len(dim)}")
            
            # 打印变量详细信息
            for var_name, var in dataset.variables.items():
                print(f"变量 '{var_name}': 形状 = {var.shape}, 维度 = {var.dimensions}")
                if hasattr(var, 'units'):
                    print(f"  单位: {var.units}")
                if hasattr(var, 'long_name'):
                    print(f"  描述: {var.long_name}")
            
            return dataset.dimensions, dataset.variables
            
    except Exception as e:
        print(f"读取NC文件时出错: {e}")
        return None, None

def write_asc_file(data, output_path, geotransform=None, nodata_value=-9999):
    """
    将数据写入ASC格式文件
    
    Parameters:
    - data: 2D numpy数组
    - output_path: 输出文件路径
    - geotransform: 地理变换参数 (xllcorner, yllcorner, cellsize)
    - nodata_value: 无数据值
    """
    nrows, ncols = data.shape
    
    # 默认地理变换参数（如果没有提供）
    if geotransform is None:
        xllcorner = 0.0
        yllcorner = 0.0
        cellsize = 1.0
    else:
        xllcorner, yllcorner, cellsize = geotransform
    
    with open(output_path, 'w') as f:
        # 写入ASC文件头
        f.write(f"ncols         {ncols}\n")
        f.write(f"nrows         {nrows}\n")
        f.write(f"xllcorner     {xllcorner}\n")
        f.write(f"yllcorner     {yllcorner}\n")
        f.write(f"cellsize      {cellsize}\n")
        f.write(f"NODATA_value  {nodata_value}\n")
        
        # 写入数据（从上到下，从左到右）
        for row in data:
            row_str = ' '.join([str(val) if not np.isnan(val) else str(nodata_value) for val in row])
            f.write(row_str + '\n')

def convert_nc_to_asc(nc_file_path, output_dir=None):
    """
    将NC文件转换为12个ASC文件
    
    Parameters:
    - nc_file_path: NC文件路径
    - output_dir: 输出目录，如果为None则使用NC文件所在目录
    """
    nc_file_path = Path(nc_file_path)
    
    if not nc_file_path.exists():
        print(f"错误: 文件不存在 {nc_file_path}")
        return False
    
    # 设置输出目录
    if output_dir is None:
        output_dir = nc_file_path.parent
    else:
        output_dir = Path(output_dir)
    
    output_dir.mkdir(exist_ok=True)
    
    try:
        with nc.Dataset(nc_file_path, 'r') as dataset:
            print(f"\n正在转换文件: {nc_file_path}")
            
            # 查找主要的数据变量（通常是PET数据）
            data_var = None
            for var_name, var in dataset.variables.items():
                if len(var.dimensions) >= 3:  # 寻找3维或更高维度的变量
                    data_var = var
                    data_var_name = var_name
                    break
            
            if data_var is None:
                print("错误: 未找到合适的数据变量")
                return False
            
            print(f"使用数据变量: {data_var_name}")
            print(f"数据形状: {data_var.shape}")
            print(f"数据维度: {data_var.dimensions}")
            
            # 获取地理变换信息
            geotransform = None
            try:
                # 尝试获取经纬度信息
                if 'lon' in dataset.variables or 'longitude' in dataset.variables:
                    lon_var = dataset.variables.get('lon') or dataset.variables.get('longitude')
                    lat_var = dataset.variables.get('lat') or dataset.variables.get('latitude')
                    
                    if lon_var is not None and lat_var is not None:
                        lon_data = lon_var[:]
                        lat_data = lat_var[:]
                        
                        # 计算地理变换参数
                        xllcorner = float(np.min(lon_data))
                        yllcorner = float(np.min(lat_data))
                        
                        # 计算像元大小
                        if len(lon_data) > 1:
                            cellsize_x = float((np.max(lon_data) - np.min(lon_data)) / (len(lon_data) - 1))
                        else:
                            cellsize_x = 1.0
                            
                        if len(lat_data) > 1:
                            cellsize_y = float((np.max(lat_data) - np.min(lat_data)) / (len(lat_data) - 1))
                        else:
                            cellsize_y = 1.0
                        
                        # 使用较小的像元大小作为统一的像元大小
                        cellsize = min(abs(cellsize_x), abs(cellsize_y))
                        
                        geotransform = (xllcorner, yllcorner, cellsize)
                        print(f"地理变换参数: xllcorner={xllcorner}, yllcorner={yllcorner}, cellsize={cellsize}")
                        
            except Exception as e:
                print(f"获取地理变换信息时出错: {e}")
                print("将使用默认地理变换参数")
            
            # 获取时间维度
            time_dim = None
            for dim_name in data_var.dimensions:
                if 'time' in dim_name.lower():
                    time_dim = dim_name
                    break
            
            if time_dim is None:
                print("警告: 未找到时间维度，假设第一个维度为时间")
                time_dim = data_var.dimensions[0]
            
            time_size = dataset.dimensions[time_dim].size
            print(f"时间维度: {time_dim}, 大小: {time_size}")
            
            # 转换每个月的数据
            base_name = nc_file_path.stem
            
            for month in range(min(12, time_size)):
                print(f"正在处理第 {month + 1} 月...")
                
                # 提取该月数据
                if len(data_var.dimensions) == 3:
                    month_data = data_var[month, :, :]
                elif len(data_var.dimensions) == 4:
                    month_data = data_var[month, 0, :, :]  # 假设第二个维度大小为1
                else:
                    print(f"不支持的数据维度: {len(data_var.dimensions)}")
                    continue
                
                # 转换为numpy数组
                month_data = np.array(month_data)
                
                # 翻转数据（NC文件通常是从北到南，ASC文件需要从南到北）
                month_data = np.flipud(month_data)
                
                # 生成输出文件名
                output_filename = f"{base_name}_month_{month + 1:02d}.asc"
                output_path = output_dir / output_filename
                
                # 写入ASC文件
                write_asc_file(month_data, output_path, geotransform)
                print(f"已生成: {output_path}")
            
            print(f"\n转换完成! 共生成 {min(12, time_size)} 个ASC文件")
            return True
            
    except Exception as e:
        print(f"转换过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("NC文件转ASC文件转换器")
    print("=" * 50)
    
    # 检查当前目录下的NC文件
    current_dir = Path('.')
    nc_files = []
    
    # 查找所有NC文件
    for nc_file in current_dir.rglob('*.nc'):
        nc_files.append(nc_file)
    
    if not nc_files:
        print("当前目录下未找到NC文件")
        return
    
    print(f"找到 {len(nc_files)} 个NC文件:")
    for i, nc_file in enumerate(nc_files, 1):
        print(f"{i}. {nc_file}")
    
    # 处理每个NC文件
    for nc_file in nc_files:
        print(f"\n{'='*60}")
        
        # 首先检查文件结构
        dims, vars = check_nc_structure(nc_file)
        
        if dims is not None and vars is not None:
            # 进行转换
            success = convert_nc_to_asc(nc_file)
            if success:
                print(f"✓ {nc_file} 转换成功")
            else:
                print(f"✗ {nc_file} 转换失败")

if __name__ == "__main__":
    main()
