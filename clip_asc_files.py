#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASC文件裁剪工具
将现有的ASC文件裁剪到指定的地理范围和像素大小
"""

import os
import sys
import numpy as np
from pathlib import Path
import re

def read_asc_file(file_path):
    """
    读取ASC文件
    
    Returns:
    - header: 字典，包含ASC文件头信息
    - data: numpy数组，包含数据
    """
    header = {}
    data_lines = []
    
    with open(file_path, 'r') as f:
        # 读取头信息
        for i in range(6):  # ASC文件通常有6行头信息
            line = f.readline().strip()
            if line:
                parts = line.split()
                if len(parts) >= 2:
                    key = parts[0].lower()
                    value = float(parts[1]) if '.' in parts[1] or 'e' in parts[1].lower() else int(parts[1])
                    header[key] = value
        
        # 读取数据
        for line in f:
            line = line.strip()
            if line:
                row_data = [float(x) for x in line.split()]
                data_lines.append(row_data)
    
    data = np.array(data_lines)
    return header, data

def write_asc_file(data, output_path, header, nodata_value=-9999):
    """
    写入ASC文件
    
    Parameters:
    - data: 2D numpy数组
    - output_path: 输出文件路径
    - header: 头信息字典
    - nodata_value: 无数据值
    """
    nrows, ncols = data.shape
    
    with open(output_path, 'w') as f:
        # 写入ASC文件头
        f.write(f"ncols         {ncols}\n")
        f.write(f"nrows         {nrows}\n")
        f.write(f"xllcorner     {header['xllcorner']}\n")
        f.write(f"yllcorner     {header['yllcorner']}\n")
        f.write(f"cellsize      {header['cellsize']}\n")
        f.write(f"NODATA_value  {nodata_value}\n")
        
        # 写入数据
        for row in data:
            row_str = ' '.join([str(val) if not np.isnan(val) and val != nodata_value else str(nodata_value) for val in row])
            f.write(row_str + '\n')

def calculate_clip_indices(original_header, target_header):
    """
    计算裁剪的行列索引范围
    
    Parameters:
    - original_header: 原始ASC文件头信息
    - target_header: 目标ASC文件头信息
    
    Returns:
    - (row_start, row_end, col_start, col_end): 裁剪索引
    """
    # 原始数据的地理范围
    orig_xmin = original_header['xllcorner']
    orig_ymin = original_header['yllcorner']
    orig_cellsize = original_header['cellsize']
    orig_ncols = original_header['ncols']
    orig_nrows = original_header['nrows']
    
    orig_xmax = orig_xmin + orig_ncols * orig_cellsize
    orig_ymax = orig_ymin + orig_nrows * orig_cellsize
    
    # 目标数据的地理范围
    target_xmin = target_header['xllcorner']
    target_ymin = target_header['yllcorner']
    target_cellsize = target_header['cellsize']
    target_ncols = target_header['ncols']
    target_nrows = target_header['nrows']
    
    target_xmax = target_xmin + target_ncols * target_cellsize
    target_ymax = target_ymin + target_nrows * target_cellsize
    
    print(f"原始数据范围: X({orig_xmin:.6f}, {orig_xmax:.6f}), Y({orig_ymin:.6f}, {orig_ymax:.6f})")
    print(f"目标数据范围: X({target_xmin:.6f}, {target_xmax:.6f}), Y({target_ymin:.6f}, {target_ymax:.6f})")
    
    # 检查是否有重叠
    if (target_xmax <= orig_xmin or target_xmin >= orig_xmax or 
        target_ymax <= orig_ymin or target_ymin >= orig_ymax):
        print("警告: 目标范围与原始数据范围没有重叠!")
        return None
    
    # 计算列索引 (X方向)
    col_start = max(0, int((target_xmin - orig_xmin) / orig_cellsize))
    col_end = min(orig_ncols, int((target_xmax - orig_xmin) / orig_cellsize) + 1)
    
    # 计算行索引 (Y方向) - 注意ASC文件是从上到下存储的
    # 原始数据的Y坐标是从下到上增加的，但数组索引是从上到下的
    row_start = max(0, int((orig_ymax - target_ymax) / orig_cellsize))
    row_end = min(orig_nrows, int((orig_ymax - target_ymin) / orig_cellsize) + 1)
    
    print(f"裁剪索引: 行({row_start}, {row_end}), 列({col_start}, {col_end})")
    
    return (row_start, row_end, col_start, col_end)

def resample_data(data, original_cellsize, target_cellsize, method='nearest'):
    """
    重采样数据到目标像元大小
    
    Parameters:
    - data: 原始数据数组
    - original_cellsize: 原始像元大小
    - target_cellsize: 目标像元大小
    - method: 重采样方法 ('nearest', 'bilinear')
    
    Returns:
    - 重采样后的数据数组
    """
    if abs(original_cellsize - target_cellsize) < 1e-10:
        return data  # 像元大小相同，不需要重采样
    
    scale_factor = original_cellsize / target_cellsize
    orig_rows, orig_cols = data.shape
    
    new_rows = int(orig_rows * scale_factor)
    new_cols = int(orig_cols * scale_factor)
    
    print(f"重采样: {orig_rows}x{orig_cols} -> {new_rows}x{new_cols} (比例: {scale_factor:.4f})")
    
    if method == 'nearest':
        # 最近邻重采样
        row_indices = np.round(np.linspace(0, orig_rows-1, new_rows)).astype(int)
        col_indices = np.round(np.linspace(0, orig_cols-1, new_cols)).astype(int)
        
        resampled_data = data[np.ix_(row_indices, col_indices)]
        
    else:
        # 简单的双线性插值（这里使用numpy的简化版本）
        from scipy import ndimage
        zoom_factor = (new_rows / orig_rows, new_cols / orig_cols)
        resampled_data = ndimage.zoom(data, zoom_factor, order=1)
    
    return resampled_data

def clip_asc_file(input_path, output_path, target_header):
    """
    裁剪单个ASC文件
    
    Parameters:
    - input_path: 输入ASC文件路径
    - output_path: 输出ASC文件路径
    - target_header: 目标头信息
    """
    print(f"\n正在处理: {input_path}")
    
    # 读取原始文件
    try:
        original_header, original_data = read_asc_file(input_path)
        print(f"原始文件大小: {original_data.shape}")
        print(f"原始像元大小: {original_header['cellsize']}")
        
    except Exception as e:
        print(f"读取文件失败: {e}")
        return False
    
    # 计算裁剪索引
    clip_indices = calculate_clip_indices(original_header, target_header)
    if clip_indices is None:
        print("无法计算裁剪范围，跳过此文件")
        return False
    
    row_start, row_end, col_start, col_end = clip_indices
    
    # 裁剪数据
    clipped_data = original_data[row_start:row_end, col_start:col_end]
    print(f"裁剪后大小: {clipped_data.shape}")
    
    # 更新头信息
    clipped_header = target_header.copy()
    
    # 如果需要重采样
    if abs(original_header['cellsize'] - target_header['cellsize']) > 1e-10:
        print("需要重采样数据...")
        try:
            clipped_data = resample_data(clipped_data, original_header['cellsize'], target_header['cellsize'])
            print(f"重采样后大小: {clipped_data.shape}")
        except Exception as e:
            print(f"重采样失败: {e}")
            return False
    
    # 确保输出目录存在
    output_path = Path(output_path)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 写入裁剪后的文件
    try:
        write_asc_file(clipped_data, output_path, clipped_header)
        print(f"已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"保存文件失败: {e}")
        return False

def validate_data(file_path, expected_header):
    """
    验证ASC文件的数据完整性
    """
    try:
        header, data = read_asc_file(file_path)
        
        print(f"\n验证文件: {file_path}")
        print(f"文件大小: {data.shape}")
        print(f"期望大小: ({expected_header['nrows']}, {expected_header['ncols']})")
        
        # 检查尺寸
        if data.shape != (expected_header['nrows'], expected_header['ncols']):
            print("❌ 文件尺寸不匹配!")
            return False
        
        # 检查头信息
        for key in ['xllcorner', 'yllcorner', 'cellsize']:
            if abs(header[key] - expected_header[key]) > 1e-6:
                print(f"❌ {key} 不匹配: {header[key]} vs {expected_header[key]}")
                return False
        
        # 检查数据统计
        valid_data = data[data != -9999]
        if len(valid_data) > 0:
            print(f"数据统计: 最小值={np.min(valid_data):.4f}, 最大值={np.max(valid_data):.4f}, 平均值={np.mean(valid_data):.4f}")
            print(f"有效数据点: {len(valid_data)}/{data.size} ({len(valid_data)/data.size*100:.1f}%)")
        else:
            print("⚠️  警告: 没有有效数据点")
        
        print("✅ 验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("ASC文件裁剪工具")
    print("=" * 50)
    
    # 目标参数
    target_header = {
        'ncols': 799,
        'nrows': 960,
        'xllcorner': 110.23,
        'yllcorner': 23.619166666986,
        'cellsize': 0.000833333333,
        'nodata_value': -9999
    }
    
    print("目标参数:")
    for key, value in target_header.items():
        print(f"  {key}: {value}")
    
    # 查找所有ASC文件
    current_dir = Path('.')
    asc_files = []
    
    for asc_file in current_dir.rglob('*.asc'):
        # 跳过已经裁剪过的文件
        if '_clipped' not in str(asc_file):
            asc_files.append(asc_file)
    
    if not asc_files:
        print("未找到ASC文件")
        return
    
    print(f"\n找到 {len(asc_files)} 个ASC文件:")
    for i, asc_file in enumerate(asc_files, 1):
        print(f"{i}. {asc_file}")
    
    # 处理每个ASC文件
    success_count = 0
    for asc_file in asc_files:
        # 生成输出文件名
        output_file = asc_file.parent / f"{asc_file.stem}_clipped.asc"
        
        # 裁剪文件
        if clip_asc_file(asc_file, output_file, target_header):
            success_count += 1
    
    print(f"\n裁剪完成! 成功处理 {success_count}/{len(asc_files)} 个文件")
    
    # 验证部分文件
    print("\n" + "="*50)
    print("数据验证")
    
    # 验证每个文件夹中的第一个和第七个月的数据
    test_files = []
    for year in ['2023', '2024']:
        for month in ['01', '07']:
            test_file = current_dir / f"pet_{year}" / f"pet_{year}_month_{month}_clipped.asc"
            if test_file.exists():
                test_files.append(test_file)
    
    for test_file in test_files:
        validate_data(test_file, target_header)

if __name__ == "__main__":
    main()
