#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据整理和处理脚本
1. 将裁剪后的ASC文件整理到新文件夹
2. 对栅格值除以10
3. 计算两年同月份的平均值
4. 输出到最终结果文件夹
"""

import os
import sys
import numpy as np
from pathlib import Path
import shutil

def read_asc_file(file_path):
    """读取ASC文件"""
    header = {}
    data_lines = []
    
    with open(file_path, 'r') as f:
        # 读取头信息
        for i in range(6):
            line = f.readline().strip()
            if line:
                parts = line.split()
                if len(parts) >= 2:
                    key = parts[0].lower()
                    value = float(parts[1]) if '.' in parts[1] or 'e' in parts[1].lower() else int(parts[1])
                    header[key] = value
        
        # 读取数据
        for line in f:
            line = line.strip()
            if line:
                row_data = [float(x) for x in line.split()]
                data_lines.append(row_data)
    
    data = np.array(data_lines)
    return header, data

def write_asc_file(data, output_path, header, nodata_value=-9999):
    """写入ASC文件"""
    nrows, ncols = data.shape
    
    with open(output_path, 'w') as f:
        f.write(f"ncols         {ncols}\n")
        f.write(f"nrows         {nrows}\n")
        f.write(f"xllcorner     {header['xllcorner']}\n")
        f.write(f"yllcorner     {header['yllcorner']}\n")
        f.write(f"cellsize      {header['cellsize']}\n")
        f.write(f"NODATA_value  {nodata_value}\n")
        
        for row in data:
            row_str = ' '.join([str(val) if not np.isnan(val) and val != nodata_value else str(nodata_value) for val in row])
            f.write(row_str + '\n')

def organize_corrected_files():
    """整理裁剪后的文件到新文件夹"""
    print("正在整理裁剪后的文件...")
    
    # 创建目标文件夹
    corrected_dir = Path("corrected_data")
    corrected_dir.mkdir(exist_ok=True)
    
    year_2023_dir = corrected_dir / "pet_2023"
    year_2024_dir = corrected_dir / "pet_2024"
    year_2023_dir.mkdir(exist_ok=True)
    year_2024_dir.mkdir(exist_ok=True)
    
    current_dir = Path('.')
    copied_count = 0
    
    # 复制2023年文件
    for month in range(1, 13):
        source_file = current_dir / f"pet_2023" / f"pet_2023_month_{month:02d}_corrected.asc"
        target_file = year_2023_dir / f"pet_2023_month_{month:02d}.asc"
        
        if source_file.exists():
            shutil.copy2(source_file, target_file)
            copied_count += 1
            print(f"  复制: {source_file} -> {target_file}")
        else:
            print(f"  警告: 源文件不存在 {source_file}")
    
    # 复制2024年文件
    for month in range(1, 13):
        source_file = current_dir / f"pet_2024" / f"pet_2024_month_{month:02d}_corrected.asc"
        target_file = year_2024_dir / f"pet_2024_month_{month:02d}.asc"
        
        if source_file.exists():
            shutil.copy2(source_file, target_file)
            copied_count += 1
            print(f"  复制: {source_file} -> {target_file}")
        else:
            print(f"  警告: 源文件不存在 {source_file}")
    
    print(f"文件整理完成，共复制了 {copied_count} 个文件")
    return corrected_dir

def process_and_average_data(corrected_dir):
    """处理数据并计算月份平均值"""
    print("\n正在处理数据并计算月份平均值...")
    
    # 创建输出文件夹
    processed_dir = Path("processed_data")
    processed_dir.mkdir(exist_ok=True)
    
    monthly_avg_dir = processed_dir / "monthly_averages"
    monthly_avg_dir.mkdir(exist_ok=True)
    
    success_count = 0
    
    for month in range(1, 13):
        print(f"\n处理第 {month} 月数据...")
        
        # 读取两年的数据
        file_2023 = corrected_dir / "pet_2023" / f"pet_2023_month_{month:02d}.asc"
        file_2024 = corrected_dir / "pet_2024" / f"pet_2024_month_{month:02d}.asc"
        
        data_2023 = None
        data_2024 = None
        header = None
        
        # 读取2023年数据
        if file_2023.exists():
            try:
                header, data_2023 = read_asc_file(file_2023)
                # 除以10
                data_2023 = data_2023 / 10.0
                print(f"  2023年数据读取成功，除以10后范围: [{np.min(data_2023[data_2023 != -999.9]):.2f}, {np.max(data_2023[data_2023 != -999.9]):.2f}]")
            except Exception as e:
                print(f"  读取2023年数据失败: {e}")
        else:
            print(f"  2023年文件不存在: {file_2023}")
        
        # 读取2024年数据
        if file_2024.exists():
            try:
                header_2024, data_2024 = read_asc_file(file_2024)
                if header is None:
                    header = header_2024
                # 除以10
                data_2024 = data_2024 / 10.0
                print(f"  2024年数据读取成功，除以10后范围: [{np.min(data_2024[data_2024 != -999.9]):.2f}, {np.max(data_2024[data_2024 != -999.9]):.2f}]")
            except Exception as e:
                print(f"  读取2024年数据失败: {e}")
        else:
            print(f"  2024年文件不存在: {file_2024}")
        
        # 计算平均值
        if data_2023 is not None and data_2024 is not None and header is not None:
            # 处理无数据值
            mask_2023 = (data_2023 != -999.9)
            mask_2024 = (data_2024 != -999.9)
            
            # 创建平均数据数组
            avg_data = np.full_like(data_2023, -999.9)
            
            # 两年都有数据的位置：计算平均值
            both_valid = mask_2023 & mask_2024
            avg_data[both_valid] = (data_2023[both_valid] + data_2024[both_valid]) / 2.0
            
            # 只有一年有数据的位置：使用该年数据
            only_2023 = mask_2023 & (~mask_2024)
            only_2024 = mask_2024 & (~mask_2023)
            avg_data[only_2023] = data_2023[only_2023]
            avg_data[only_2024] = data_2024[only_2024]
            
            # 统计信息
            valid_avg = avg_data[avg_data != -999.9]
            both_count = np.sum(both_valid)
            only_2023_count = np.sum(only_2023)
            only_2024_count = np.sum(only_2024)
            
            print(f"  平均值计算完成:")
            print(f"    两年都有数据: {both_count} 个像素")
            print(f"    仅2023年有数据: {only_2023_count} 个像素")
            print(f"    仅2024年有数据: {only_2024_count} 个像素")
            print(f"    平均值范围: [{np.min(valid_avg):.2f}, {np.max(valid_avg):.2f}]")
            print(f"    平均值均值: {np.mean(valid_avg):.2f}")
            
            # 保存结果
            output_file = monthly_avg_dir / f"pet_month_{month:02d}_average.asc"
            write_asc_file(avg_data, output_file, header, nodata_value=-999.9)
            print(f"  已保存: {output_file}")
            success_count += 1
            
        elif data_2023 is not None and header is not None:
            # 只有2023年数据
            data_2023_processed = data_2023.copy()
            output_file = monthly_avg_dir / f"pet_month_{month:02d}_average.asc"
            write_asc_file(data_2023_processed, output_file, header, nodata_value=-999.9)
            print(f"  仅使用2023年数据，已保存: {output_file}")
            success_count += 1
            
        elif data_2024 is not None and header is not None:
            # 只有2024年数据
            data_2024_processed = data_2024.copy()
            output_file = monthly_avg_dir / f"pet_month_{month:02d}_average.asc"
            write_asc_file(data_2024_processed, output_file, header, nodata_value=-999.9)
            print(f"  仅使用2024年数据，已保存: {output_file}")
            success_count += 1
            
        else:
            print(f"  第{month}月数据处理失败：缺少必要数据")
    
    print(f"\n数据处理完成，成功处理了 {success_count} 个月份")
    return monthly_avg_dir

def validate_processed_data(monthly_avg_dir):
    """验证处理后的数据"""
    print("\n验证处理后的数据...")
    print("-" * 60)
    
    for month in range(1, 13):
        file_path = monthly_avg_dir / f"pet_month_{month:02d}_average.asc"
        
        if file_path.exists():
            try:
                header, data = read_asc_file(file_path)
                valid_data = data[data != -999.9]
                
                if len(valid_data) > 0:
                    print(f"第{month:2d}月: 文件大小={data.shape}, 有效像素={len(valid_data):>6}, "
                          f"均值={np.mean(valid_data):>6.2f}, 范围=[{np.min(valid_data):>6.2f}, {np.max(valid_data):>6.2f}]")
                else:
                    print(f"第{month:2d}月: 文件大小={data.shape}, 无有效数据")
                    
            except Exception as e:
                print(f"第{month:2d}月: 读取失败 - {e}")
        else:
            print(f"第{month:2d}月: 文件不存在")

def create_summary_report(monthly_avg_dir):
    """创建处理总结报告"""
    print("\n创建处理总结报告...")
    
    report_content = """# PET数据处理总结报告

## 处理流程

1. **文件整理**: 将裁剪后的ASC文件复制到 `corrected_data/` 文件夹
2. **数值处理**: 将所有栅格值除以10（单位转换）
3. **月份平均**: 计算2023年和2024年同月份的平均值
4. **结果输出**: 生成12个月份平均ASC文件

## 输出文件结构

```
corrected_data/           # 整理后的裁剪数据
├── pet_2023/            # 2023年数据
│   ├── pet_2023_month_01.asc
│   └── ...
└── pet_2024/            # 2024年数据
    ├── pet_2024_month_01.asc
    └── ...

processed_data/          # 处理结果
└── monthly_averages/    # 月份平均值
    ├── pet_month_01_average.asc
    ├── pet_month_02_average.asc
    └── ...
```

## 处理说明

- **数值转换**: 原始值除以10，单位从0.1mm转换为mm
- **平均计算**: 对于每个像素位置，计算两年同月份的算术平均值
- **缺失处理**: 如果某年某月数据缺失，使用另一年的数据
- **无数据值**: 使用-999.9作为无数据标识

## 最终结果

生成了12个月份的平均PET栅格文件，可直接在GIS软件中使用。

文件命名规则: `pet_month_MM_average.asc` (MM为01-12)

## 数据特征

处理后的数据具有以下特征：
- 数值范围更加合理（除以10后）
- 减少了年际变异性（通过平均）
- 保持了季节性变化规律
- 适合长期气候分析使用
"""
    
    report_file = Path("数据处理总结报告.md")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"总结报告已保存: {report_file}")

def main():
    """主函数"""
    print("PET数据整理和处理工具")
    print("=" * 50)
    
    try:
        # 步骤1: 整理裁剪后的文件
        corrected_dir = organize_corrected_files()
        
        # 步骤2: 处理数据并计算月份平均值
        monthly_avg_dir = process_and_average_data(corrected_dir)
        
        # 步骤3: 验证处理后的数据
        validate_processed_data(monthly_avg_dir)
        
        # 步骤4: 创建总结报告
        create_summary_report(monthly_avg_dir)
        
        print("\n" + "="*50)
        print("处理完成!")
        print("="*50)
        print(f"✅ 整理后的数据位于: {corrected_dir}")
        print(f"✅ 最终结果位于: {monthly_avg_dir}")
        print("✅ 所有栅格值已除以10")
        print("✅ 已计算两年同月份平均值")
        print("✅ 生成了12个月份平均ASC文件")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
