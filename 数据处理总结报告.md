# PET数据处理总结报告

## 处理流程

1. **文件整理**: 将裁剪后的ASC文件复制到 `corrected_data/` 文件夹
2. **数值处理**: 将所有栅格值除以10（单位转换）
3. **月份平均**: 计算2023年和2024年同月份的平均值
4. **结果输出**: 生成12个月份平均ASC文件

## 输出文件结构

```
corrected_data/           # 整理后的裁剪数据
├── pet_2023/            # 2023年数据
│   ├── pet_2023_month_01.asc
│   └── ...
└── pet_2024/            # 2024年数据
    ├── pet_2024_month_01.asc
    └── ...

processed_data/          # 处理结果
└── monthly_averages/    # 月份平均值
    ├── pet_month_01_average.asc
    ├── pet_month_02_average.asc
    └── ...
```

## 处理说明

- **数值转换**: 原始值除以10，单位从0.1mm转换为mm
- **平均计算**: 对于每个像素位置，计算两年同月份的算术平均值
- **缺失处理**: 如果某年某月数据缺失，使用另一年的数据
- **无数据值**: 使用-999.9作为无数据标识

## 最终结果

生成了12个月份的平均PET栅格文件，可直接在GIS软件中使用。

文件命名规则: `pet_month_MM_average.asc` (MM为01-12)

## 数据特征

处理后的数据具有以下特征：
- 数值范围更加合理（除以10后）
- 减少了年际变异性（通过平均）
- 保持了季节性变化规律
- 适合长期气候分析使用
