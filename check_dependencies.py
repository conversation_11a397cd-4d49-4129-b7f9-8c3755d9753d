#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查转换脚本所需的依赖库
"""

import sys

def check_dependencies():
    """检查所需的Python库是否已安装"""
    required_packages = {
        'numpy': 'numpy',
        'netCDF4': 'netcdf4'
    }
    
    missing_packages = []
    
    print("检查Python依赖库...")
    print("-" * 40)
    
    for package_name, pip_name in required_packages.items():
        try:
            __import__(package_name)
            print(f"✓ {package_name} - 已安装")
        except ImportError:
            print(f"✗ {package_name} - 未安装")
            missing_packages.append(pip_name)
    
    print("-" * 40)
    
    if missing_packages:
        print(f"\n需要安装以下库:")
        for package in missing_packages:
            print(f"  pip install {package}")
        
        print(f"\n或者一次性安装所有缺失的库:")
        print(f"  pip install {' '.join(missing_packages)}")
        
        return False
    else:
        print("\n所有依赖库都已安装，可以运行转换脚本!")
        return True

if __name__ == "__main__":
    check_dependencies()
