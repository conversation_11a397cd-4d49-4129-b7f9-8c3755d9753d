#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确ASC文件裁剪工具
将现有的ASC文件精确裁剪到指定的地理范围和像素大小
"""

import os
import sys
import numpy as np
from pathlib import Path
import re

def read_asc_file(file_path):
    """读取ASC文件"""
    header = {}
    data_lines = []
    
    with open(file_path, 'r') as f:
        # 读取头信息
        for i in range(6):
            line = f.readline().strip()
            if line:
                parts = line.split()
                if len(parts) >= 2:
                    key = parts[0].lower()
                    value = float(parts[1]) if '.' in parts[1] or 'e' in parts[1].lower() else int(parts[1])
                    header[key] = value
        
        # 读取数据
        for line in f:
            line = line.strip()
            if line:
                row_data = [float(x) for x in line.split()]
                data_lines.append(row_data)
    
    data = np.array(data_lines)
    return header, data

def write_asc_file(data, output_path, header, nodata_value=-9999):
    """写入ASC文件"""
    nrows, ncols = data.shape
    
    with open(output_path, 'w') as f:
        f.write(f"ncols         {ncols}\n")
        f.write(f"nrows         {nrows}\n")
        f.write(f"xllcorner     {header['xllcorner']}\n")
        f.write(f"yllcorner     {header['yllcorner']}\n")
        f.write(f"cellsize      {header['cellsize']}\n")
        f.write(f"NODATA_value  {nodata_value}\n")
        
        for row in data:
            row_str = ' '.join([str(val) if not np.isnan(val) and val != nodata_value else str(nodata_value) for val in row])
            f.write(row_str + '\n')

def create_target_grid(target_header):
    """
    根据目标参数创建精确的目标网格
    """
    ncols = target_header['ncols']
    nrows = target_header['nrows']
    xllcorner = target_header['xllcorner']
    yllcorner = target_header['yllcorner']
    cellsize = target_header['cellsize']
    
    # 创建目标网格的坐标
    x_coords = np.linspace(xllcorner + cellsize/2, 
                          xllcorner + (ncols-1)*cellsize + cellsize/2, 
                          ncols)
    y_coords = np.linspace(yllcorner + cellsize/2, 
                          yllcorner + (nrows-1)*cellsize + cellsize/2, 
                          nrows)
    
    # 注意：ASC文件的Y坐标是从上到下递减的
    y_coords = y_coords[::-1]
    
    return x_coords, y_coords

def interpolate_to_target_grid(original_header, original_data, target_header):
    """
    将原始数据插值到目标网格
    """
    # 原始网格参数
    orig_ncols = original_header['ncols']
    orig_nrows = original_header['nrows']
    orig_xllcorner = original_header['xllcorner']
    orig_yllcorner = original_header['yllcorner']
    orig_cellsize = original_header['cellsize']
    
    # 创建原始网格坐标
    orig_x_coords = np.linspace(orig_xllcorner + orig_cellsize/2,
                               orig_xllcorner + (orig_ncols-1)*orig_cellsize + orig_cellsize/2,
                               orig_ncols)
    orig_y_coords = np.linspace(orig_yllcorner + orig_cellsize/2,
                               orig_yllcorner + (orig_nrows-1)*orig_cellsize + orig_cellsize/2,
                               orig_nrows)
    orig_y_coords = orig_y_coords[::-1]  # ASC文件Y坐标从上到下
    
    # 创建目标网格坐标
    target_x_coords, target_y_coords = create_target_grid(target_header)
    
    print(f"原始网格范围: X({orig_x_coords[0]:.6f}, {orig_x_coords[-1]:.6f}), Y({orig_y_coords[-1]:.6f}, {orig_y_coords[0]:.6f})")
    print(f"目标网格范围: X({target_x_coords[0]:.6f}, {target_x_coords[-1]:.6f}), Y({target_y_coords[-1]:.6f}, {target_y_coords[0]:.6f})")
    
    # 创建目标数据数组
    target_data = np.full((target_header['nrows'], target_header['ncols']), -9999.0)
    
    # 对每个目标网格点进行插值
    for i, target_y in enumerate(target_y_coords):
        for j, target_x in enumerate(target_x_coords):
            # 检查目标点是否在原始网格范围内
            if (target_x >= orig_x_coords[0] - orig_cellsize/2 and 
                target_x <= orig_x_coords[-1] + orig_cellsize/2 and
                target_y >= orig_y_coords[-1] - orig_cellsize/2 and 
                target_y <= orig_y_coords[0] + orig_cellsize/2):
                
                # 找到最近的原始网格点
                x_idx = np.argmin(np.abs(orig_x_coords - target_x))
                y_idx = np.argmin(np.abs(orig_y_coords - target_y))
                
                # 使用最近邻插值
                if (0 <= y_idx < orig_nrows and 0 <= x_idx < orig_ncols):
                    target_data[i, j] = original_data[y_idx, x_idx]
    
    return target_data

def clip_asc_file_precise(input_path, output_path, target_header):
    """
    精确裁剪ASC文件到目标规格
    """
    print(f"\n正在精确处理: {input_path}")
    
    try:
        # 读取原始文件
        original_header, original_data = read_asc_file(input_path)
        print(f"原始文件大小: {original_data.shape}")
        
        # 插值到目标网格
        target_data = interpolate_to_target_grid(original_header, original_data, target_header)
        print(f"目标文件大小: {target_data.shape}")
        
        # 确保输出目录存在
        output_path = Path(output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        write_asc_file(target_data, output_path, target_header)
        print(f"已保存: {output_path}")
        return True
        
    except Exception as e:
        print(f"处理失败: {e}")
        return False

def validate_data(file_path, expected_header):
    """验证ASC文件的数据完整性"""
    try:
        header, data = read_asc_file(file_path)
        
        print(f"\n验证文件: {file_path}")
        print(f"文件大小: {data.shape}")
        print(f"期望大小: ({expected_header['nrows']}, {expected_header['ncols']})")
        
        # 检查尺寸
        if data.shape != (expected_header['nrows'], expected_header['ncols']):
            print("❌ 文件尺寸不匹配!")
            return False
        
        # 检查头信息
        for key in ['xllcorner', 'yllcorner', 'cellsize']:
            if abs(header[key] - expected_header[key]) > 1e-9:
                print(f"❌ {key} 不匹配: {header[key]} vs {expected_header[key]}")
                return False
        
        # 检查数据统计
        valid_data = data[data != -9999]
        if len(valid_data) > 0:
            print(f"数据统计: 最小值={np.min(valid_data):.4f}, 最大值={np.max(valid_data):.4f}, 平均值={np.mean(valid_data):.4f}")
            print(f"有效数据点: {len(valid_data)}/{data.size} ({len(valid_data)/data.size*100:.1f}%)")
        else:
            print("⚠️  警告: 没有有效数据点")
        
        print("✅ 验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("精确ASC文件裁剪工具")
    print("=" * 50)
    
    # 目标参数
    target_header = {
        'ncols': 799,
        'nrows': 960,
        'xllcorner': 110.23,
        'yllcorner': 23.619166666986,
        'cellsize': 0.000833333333,
        'nodata_value': -9999
    }
    
    print("目标参数:")
    for key, value in target_header.items():
        print(f"  {key}: {value}")
    
    # 查找所有原始ASC文件（不包括已裁剪的）
    current_dir = Path('.')
    asc_files = []
    
    for asc_file in current_dir.rglob('*.asc'):
        if '_clipped' not in str(asc_file) and '_corrected' not in str(asc_file):
            asc_files.append(asc_file)
    
    if not asc_files:
        print("未找到原始ASC文件")
        return
    
    print(f"\n找到 {len(asc_files)} 个原始ASC文件")
    
    # 处理每个ASC文件
    success_count = 0
    for asc_file in asc_files:
        # 生成输出文件名
        output_file = asc_file.parent / f"{asc_file.stem}_corrected.asc"
        
        # 精确裁剪文件
        if clip_asc_file_precise(asc_file, output_file, target_header):
            success_count += 1
    
    print(f"\n精确裁剪完成! 成功处理 {success_count}/{len(asc_files)} 个文件")
    
    # 验证部分文件
    print("\n" + "="*50)
    print("数据验证")
    
    # 验证每个文件夹中的第一个和第七个月的数据
    test_files = []
    for year in ['2023', '2024']:
        for month in ['01', '07']:
            test_file = current_dir / f"pet_{year}" / f"pet_{year}_month_{month}_corrected.asc"
            if test_file.exists():
                test_files.append(test_file)
    
    for test_file in test_files:
        validate_data(test_file, target_header)

if __name__ == "__main__":
    main()
